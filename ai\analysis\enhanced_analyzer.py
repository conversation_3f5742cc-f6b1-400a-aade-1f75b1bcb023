#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
增强版数据分析器
优化数据分析流程，提升分析准确性和性能
"""
import os
import json
import logging
import time
import hashlib
from typing import Dict, Any, List, Optional, Tuple
from concurrent.futures import ThreadPoolExecutor, as_completed
from threading import Lock
import asyncio
from datetime import datetime

from ..adapter import AIAdapter
from ..schema import validate_and_fix, SchemaValidationError
from ..prompt_loader import PromptLoader
from ..tagger import assign_tags_to_report, generate_tags
from ..anomaly_detector import detect_anomalies
from .rag_adapter import RAGAdapter

logger = logging.getLogger(__name__)


class AnalysisCache:
    """分析结果缓存器"""

    def __init__(self, max_size: int = 1000, ttl: int = 3600):
        self.cache: Dict[str, Dict[str, Any]] = {}
        self.access_times: Dict[str, float] = {}
        self.max_size = max_size
        self.ttl = ttl
        self.lock = Lock()

    def _generate_key(
        self, text: str, department: Optional[str] = None, role: Optional[str] = None
    ) -> str:
        """生成缓存键"""
        content = f"{text}|{department or ''}|{role or ''}"
        return hashlib.md5(content.encode()).hexdigest()

    def get(
        self, text: str, department: Optional[str] = None, role: Optional[str] = None
    ) -> Optional[Dict[str, Any]]:
        """获取缓存结果"""
        key = self._generate_key(text, department, role)

        with self.lock:
            if key in self.cache:
                # 检查是否过期
                if time.time() - self.access_times[key] < self.ttl:
                    self.access_times[key] = time.time()
                    return self.cache[key].copy()
                else:
                    # 过期，删除
                    del self.cache[key]
                    del self.access_times[key]

        return None

    def set(
        self,
        text: str,
        result: Dict[str, Any],
        department: Optional[str] = None,
        role: Optional[str] = None,
    ):
        """设置缓存结果"""
        key = self._generate_key(text, department, role)

        with self.lock:
            # 如果缓存已满，删除最旧的条目
            if len(self.cache) >= self.max_size:
                oldest_key = min(
                    self.access_times.keys(), key=lambda k: self.access_times[k]
                )
                del self.cache[oldest_key]
                del self.access_times[oldest_key]

            self.cache[key] = result.copy()
            self.access_times[key] = time.time()


class ModelPerformanceTracker:
    """模型性能跟踪器"""

    def __init__(self):
        self.metrics = {}
        self.lock = Lock()

    def record_call(
        self,
        model_name: str,
        response_time: float,
        success: bool,
        token_count: int = 0,
        analysis_quality: float = 0.0,
    ):
        """记录模型调用"""
        with self.lock:
            if model_name not in self.metrics:
                self.metrics[model_name] = {
                    "total_calls": 0,
                    "successful_calls": 0,
                    "total_response_time": 0,
                    "total_tokens": 0,
                    "error_count": 0,
                    "quality_scores": [],
                }

            stats = self.metrics[model_name]
            stats["total_calls"] += 1
            stats["total_response_time"] += response_time
            stats["total_tokens"] += token_count

            if success:
                stats["successful_calls"] += 1
                if analysis_quality > 0:
                    stats["quality_scores"].append(analysis_quality)
            else:
                stats["error_count"] += 1

    def get_best_model(self) -> Optional[str]:
        """获取性能最佳的模型"""
        with self.lock:
            if not self.metrics:
                return None

            best_model = None
            best_score = 0

            for model_name, stats in self.metrics.items():
                if stats["total_calls"] < 5:  # 需要足够的样本
                    continue

                success_rate = stats["successful_calls"] / stats["total_calls"]
                avg_response_time = stats["total_response_time"] / stats["total_calls"]
                avg_quality = (
                    sum(stats["quality_scores"]) / len(stats["quality_scores"])
                    if stats["quality_scores"]
                    else 0.5
                )

                # 综合评分：成功率40%，质量30%，响应时间30%
                score = (
                    success_rate * 0.4
                    + avg_quality * 0.3
                    + (1 / (avg_response_time + 1)) * 0.3
                )

                if score > best_score:
                    best_score = score
                    best_model = model_name

            return best_model


class AnalysisQualityEvaluator:
    """分析质量评估器"""

    def __init__(self):
        self.quality_metrics = {
            "completeness": 0.3,  # 完整性权重
            "accuracy": 0.4,  # 准确性权重
            "consistency": 0.3,  # 一致性权重
        }

    def evaluate_analysis_quality(
        self, result: Dict[str, Any], original_text: str
    ) -> float:
        """评估分析质量"""
        try:
            completeness_score = self._evaluate_completeness(result)
            accuracy_score = self._evaluate_accuracy(result, original_text)
            consistency_score = self._evaluate_consistency(result)

            total_score = (
                completeness_score * self.quality_metrics["completeness"]
                + accuracy_score * self.quality_metrics["accuracy"]
                + consistency_score * self.quality_metrics["consistency"]
            )

            return min(max(total_score, 0.0), 1.0)  # 确保在0-1范围内

        except Exception as e:
            logger.error(f"质量评估失败: {e}")
            return 0.5  # 默认中等质量

    def _evaluate_completeness(self, result: Dict[str, Any]) -> float:
        """评估完整性"""
        required_fields = [
            "employee",
            "week",
            "work_items",
            "summary",
            "metrics",
            "tags",
            "anomaly_flags",
        ]

        score = 0.0
        for field in required_fields:
            if field in result and result[field]:
                if field == "work_items" and isinstance(result[field], list):
                    score += 1.0 / len(required_fields)
                elif field == "metrics" and isinstance(result[field], dict):
                    # 检查关键指标
                    key_metrics = ["total_hours", "task_count"]
                    metrics_score = sum(
                        1 for m in key_metrics if m in result[field]
                    ) / len(key_metrics)
                    score += (1.0 / len(required_fields)) * metrics_score
                else:
                    score += 1.0 / len(required_fields)

        return score

    def _evaluate_accuracy(self, result: Dict[str, Any], original_text: str) -> float:
        """评估准确性"""
        # 简单的准确性检查
        score = 0.5  # 基础分

        # 检查工作项是否与原文相关
        if "work_items" in result and isinstance(result["work_items"], list):
            work_items_text = " ".join(
                [
                    item.get("title", "") + " " + item.get("description", "")
                    for item in result["work_items"]
                    if isinstance(item, dict)
                ]
            )

            # 简单的关键词匹配检查
            original_words = set(original_text.lower().split())
            work_words = set(work_items_text.lower().split())

            if original_words and work_words:
                overlap = len(original_words & work_words) / len(original_words)
                score += overlap * 0.3

        # 检查总结是否合理
        if "summary" in result and isinstance(result["summary"], dict):
            summary_text = str(result["summary"].get("main_achievements", ""))
            if len(summary_text) > 10:  # 有实际内容
                score += 0.2

        return min(score, 1.0)

    def _evaluate_consistency(self, result: Dict[str, Any]) -> float:
        """评估一致性"""
        score = 0.5  # 基础分

        # 检查工作项数量与task_count的一致性
        if (
            "work_items" in result
            and "metrics" in result
            and isinstance(result["work_items"], list)
            and isinstance(result["metrics"], dict)
        ):

            actual_count = len(result["work_items"])
            reported_count = result["metrics"].get("task_count", 0)

            if actual_count > 0 and reported_count > 0:
                consistency_ratio = min(actual_count, reported_count) / max(
                    actual_count, reported_count
                )
                score += consistency_ratio * 0.3

        # 检查工时一致性
        if (
            "work_items" in result
            and "metrics" in result
            and isinstance(result["work_items"], list)
            and isinstance(result["metrics"], dict)
        ):

            total_hours_from_items = sum(
                item.get("duration_hours", 0)
                for item in result["work_items"]
                if isinstance(item, dict)
            )
            reported_total_hours = result["metrics"].get("total_hours", 0)

            if total_hours_from_items > 0 and reported_total_hours > 0:
                hours_consistency = min(
                    total_hours_from_items, reported_total_hours
                ) / max(total_hours_from_items, reported_total_hours)
                score += hours_consistency * 0.2

        return min(score, 1.0)


class EnhancedDataAnalyzer:
    """增强版数据分析器"""

    def __init__(
        self,
        ai_config="ai/ai_config.json",
        rag_config=None,
        enable_cache=True,
        enable_performance_tracking=True,
    ):
        self.logger = logging.getLogger(__name__)

        # 初始化组件
        self.adapter = AIAdapter(ai_config)
        self.prompt_loader = PromptLoader()
        self.rag_adapter = None

        # 性能优化组件
        self.performance_tracker = (
            ModelPerformanceTracker() if enable_performance_tracking else None
        )
        self.analysis_cache = AnalysisCache() if enable_cache else None
        self.quality_evaluator = AnalysisQualityEvaluator()

        # 批处理配置
        self.max_workers = 3  # 并发处理数量
        self.batch_size = 10  # 批处理大小

        # 初始化RAG适配器
        if rag_config:
            try:
                self.rag_adapter = RAGAdapter(rag_config, self.logger)
                self.logger.info("RAG适配器初始化成功")
            except Exception as e:
                self.logger.error(f"RAG适配器初始化失败: {e}")

    def _optimize_prompt(self, prompt: str, report_text: str) -> str:
        """优化提示词，减少token消耗"""
        # 移除多余的空白字符
        prompt = " ".join(prompt.split())

        # 如果报告文本过长，进行智能截取
        if len(report_text) > 2000:
            # 保留开头和结尾，中间部分进行摘要
            start = report_text[:800]
            end = report_text[-800:]
            middle_summary = f"[中间部分摘要: 约{len(report_text)-1600}字符]"
            report_text = f"{start}\n{middle_summary}\n{end}"

        return prompt.replace("{content}", report_text)

    def _call_ai_with_tracking(
        self, prompt: str, model_name: Optional[str] = None
    ) -> Tuple[str, bool, float]:
        """调用AI并跟踪性能"""
        start_time = time.time()
        success = False
        result = ""

        try:
            result = self.adapter.call(prompt)
            success = True
        except Exception as e:
            self.logger.error(f"AI调用失败: {e}")
            result = str(e)

        response_time = time.time() - start_time

        return result, success, response_time

    def analyze(
        self,
        report_text: str,
        department: Optional[str] = None,
        role: Optional[str] = None,
        use_rag: bool = True,
        use_cache: bool = True,
    ) -> Dict[str, Any]:
        """
        增强版分析方法

        Args:
            report_text: 周报文本内容
            department: 部门
            role: 岗位
            use_rag: 是否使用RAG检索增强
            use_cache: 是否使用缓存

        Returns:
            分析结果字典
        """
        # 检查缓存
        if use_cache and self.analysis_cache:
            cached_result = self.analysis_cache.get(report_text, department, role)
            if cached_result:
                self.logger.info("使用缓存结果")
                return cached_result

        try:
            # 1. 模板选择和加载
            key = f'{department or ""}{"-" if department and role else ""}{role or ""}.json'
            self.logger.info(f"使用模板: {key}")

            prompt_cfg = self.prompt_loader.load(key)
            prompt = prompt_cfg["template"]

            # 2. RAG检索增强
            if use_rag and self.rag_adapter:
                prompt = self._enhance_with_rag(prompt, report_text, department, role)

            # 3. 提示词优化
            optimized_prompt = self._optimize_prompt(prompt, report_text)

            # 4. AI调用
            model_name = prompt_cfg.get("model", "default")
            ai_output, success, response_time = self._call_ai_with_tracking(
                optimized_prompt, model_name
            )

            if not success:
                raise ValueError(f"AI调用失败: {ai_output}")

            # 5. 解析结果
            try:
                result = json.loads(ai_output)
            except Exception as e:
                raise ValueError(f"AI输出非JSON: {e}\n原始输出: {ai_output}")

            # 6. 后处理
            result = self._post_process_result(
                result, prompt_cfg, report_text, department, role
            )

            # 7. 质量评估
            quality_score = self.quality_evaluator.evaluate_analysis_quality(
                result, report_text
            )
            result["quality_score"] = quality_score

            # 8. 性能跟踪
            if self.performance_tracker:
                token_count = len(optimized_prompt.split()) + len(ai_output.split())
                self.performance_tracker.record_call(
                    model_name, response_time, True, token_count, quality_score
                )

            # 9. 缓存结果
            if use_cache and self.analysis_cache:
                self.analysis_cache.set(report_text, result, department, role)

            self.logger.info(
                f"分析完成，耗时: {response_time:.2f}秒，质量评分: {quality_score:.2f}"
            )
            return result

        except Exception as e:
            self.logger.error(f"分析失败: {e}")
            # 记录失败的性能数据
            if self.performance_tracker:
                self.performance_tracker.record_call(
                    model_name if "model_name" in locals() else "unknown",
                    response_time if "response_time" in locals() else 0,
                    False,
                )
            raise

    def _enhance_with_rag(
        self, prompt: str, report_text: str, department: str, role: str
    ) -> str:
        """使用RAG增强提示词"""
        try:
            query = f"部门:{department} 岗位:{role} {report_text[:200]}"
            rag_results = self.rag_adapter.query(query)

            if isinstance(rag_results, dict) and "error" not in rag_results:
                context = "\n\n相关背景知识：\n"
                if "knowledge" in rag_results and isinstance(
                    rag_results["knowledge"], list
                ):
                    for i, item in enumerate(rag_results["knowledge"]):
                        if isinstance(item, dict) and "content" in item:
                            context += f"{i+1}. {item['content']}\n"

                if context != "\n\n相关背景知识：\n":
                    prompt = prompt.replace("{content}", f"{context}\n\n{{content}}")
                    self.logger.info("成功使用RAG增强提示词")
            else:
                self.logger.warning(f"RAG检索失败: {rag_results}")

        except Exception as e:
            self.logger.error(f"RAG增强失败: {e}")

        return prompt

    def _post_process_result(
        self,
        result: Dict[str, Any],
        prompt_cfg: Dict[str, Any],
        report_text: str,
        department: str,
        role: str,
    ) -> Dict[str, Any]:
        """后处理分析结果"""
        # Schema校验
        validate_and_fix(result)

        # 标签分配
        ai_tags = result.get("tags", [])
        generated_tags = generate_tags(result)
        keyword_tags = assign_tags_to_report(result)
        all_tags = list(set(ai_tags + generated_tags + keyword_tags))
        result["tags"] = all_tags

        # 异常检测
        result["anomaly_flags"] = detect_anomalies(result)

        # 专项分析补全
        for field in ["innovation_analysis", "quality_analysis", "trend_analysis"]:
            if field not in result or not result[field]:
                result[field] = ""

        # 其他补全
        result["ai_version"] = prompt_cfg.get("ai_version", "auto")
        result["raw_text"] = report_text
        result["analysis_timestamp"] = datetime.now().isoformat()

        # 员工信息补全
        if "employee" not in result or not isinstance(result["employee"], dict):
            result["employee"] = {}
        result["employee"].setdefault("name", prompt_cfg.get("name", ""))
        result["employee"].setdefault("email", prompt_cfg.get("email", ""))
        result["employee"].setdefault("department", department or "")
        result["employee"].setdefault("role", role or "")

        # 周次补全
        if not result.get("week"):
            result["week"] = prompt_cfg.get("week", "")

        # 指标计算补全
        self._calculate_metrics(result, role)

        return result

    def _calculate_metrics(self, result: Dict[str, Any], role: str):
        """计算指标"""
        metrics = result.get("metrics", {})
        if not isinstance(metrics, dict):
            metrics = {}
            result["metrics"] = metrics

        # 计算饱和度
        if "saturation" not in metrics and "total_hours" in metrics:
            role_standard_hours = {
                "工程师": 40,
                "技术支持": 40,
                "销售": 35,
                "客服": 38,
                "管理": 45,
            }
            standard = role_standard_hours.get(role, 40)
            metrics["saturation"] = round(metrics["total_hours"] / standard, 2)

            # 饱和度标签
            if metrics["saturation"] > 1.2:
                metrics["saturation_tag"] = "过载"
            elif metrics["saturation"] > 0.9:
                metrics["saturation_tag"] = "饱和"
            elif metrics["saturation"] > 0.7:
                metrics["saturation_tag"] = "适中"
            else:
                metrics["saturation_tag"] = "不足"

    def batch_analyze(
        self, reports: List[Dict[str, Any]], use_parallel: bool = True
    ) -> List[Dict[str, Any]]:
        """
        批量分析报告

        Args:
            reports: 报告列表，每个报告包含 {'text': str, 'department': str, 'role': str}
            use_parallel: 是否使用并行处理

        Returns:
            分析结果列表
        """
        if not reports:
            return []

        self.logger.info(f"开始批量分析 {len(reports)} 个报告")
        start_time = time.time()

        if use_parallel and len(reports) > 1:
            results = self._parallel_batch_analyze(reports)
        else:
            results = self._sequential_batch_analyze(reports)

        total_time = time.time() - start_time
        self.logger.info(f"批量分析完成，总耗时: {total_time:.2f}秒")

        return results

    def _sequential_batch_analyze(
        self, reports: List[Dict[str, Any]]
    ) -> List[Dict[str, Any]]:
        """顺序批量分析"""
        results = []
        for i, report in enumerate(reports):
            try:
                self.logger.info(f"分析报告 {i+1}/{len(reports)}")
                result = self.analyze(
                    report["text"], report.get("department"), report.get("role")
                )
                results.append(result)
            except Exception as e:
                self.logger.error(f"分析报告 {i+1} 失败: {e}")
                results.append({"error": str(e), "report_index": i})

        return results

    def _parallel_batch_analyze(
        self, reports: List[Dict[str, Any]]
    ) -> List[Dict[str, Any]]:
        """并行批量分析"""
        results = [None] * len(reports)

        with ThreadPoolExecutor(max_workers=self.max_workers) as executor:
            # 提交任务
            future_to_index = {}
            for i, report in enumerate(reports):
                future = executor.submit(
                    self.analyze,
                    report["text"],
                    report.get("department"),
                    report.get("role"),
                )
                future_to_index[future] = i

            # 收集结果
            for future in as_completed(future_to_index):
                index = future_to_index[future]
                try:
                    result = future.result()
                    results[index] = result
                    self.logger.info(f"完成报告 {index+1}/{len(reports)}")
                except Exception as e:
                    self.logger.error(f"分析报告 {index+1} 失败: {e}")
                    results[index] = {"error": str(e), "report_index": index}

        return results

    def get_performance_statistics(self) -> Dict[str, Any]:
        """获取性能统计信息"""
        stats = {}

        if self.performance_tracker:
            stats["model_performance"] = self.performance_tracker.metrics
            stats["best_model"] = self.performance_tracker.get_best_model()

        if self.analysis_cache:
            with self.analysis_cache.lock:
                stats["cache_statistics"] = {
                    "cache_size": len(self.analysis_cache.cache),
                    "max_size": self.analysis_cache.max_size,
                    "cache_usage": len(self.analysis_cache.cache)
                    / self.analysis_cache.max_size,
                }

        return stats

    def clear_cache(self):
        """清空缓存"""
        if self.analysis_cache:
            with self.analysis_cache.lock:
                self.analysis_cache.cache.clear()
                self.analysis_cache.access_times.clear()
            self.logger.info("分析缓存已清空")

    def optimize_model_selection(self) -> str:
        """优化模型选择"""
        if self.performance_tracker:
            best_model = self.performance_tracker.get_best_model()
            if best_model:
                self.logger.info(f"推荐使用模型: {best_model}")
                return best_model

        return "default"

    def get_analysis_insights(self) -> Dict[str, Any]:
        """获取分析洞察"""
        insights = {
            "total_analyses": 0,
            "avg_quality_score": 0.0,
            "common_anomalies": [],
            "performance_trends": {},
        }

        if self.performance_tracker:
            with self.performance_tracker.lock:
                total_calls = sum(
                    stats["total_calls"]
                    for stats in self.performance_tracker.metrics.values()
                )
                total_quality_scores = []

                for stats in self.performance_tracker.metrics.values():
                    total_quality_scores.extend(stats["quality_scores"])

                insights["total_analyses"] = total_calls
                if total_quality_scores:
                    insights["avg_quality_score"] = sum(total_quality_scores) / len(
                        total_quality_scores
                    )

        return insights
