#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
集成数据分析器
整合增强分析器、流程优化器、智能调度器，提供统一的分析接口
"""
import logging
import time
import json
from typing import Dict, Any, List, Optional, Callable
from datetime import datetime
import asyncio
import threading

from .enhanced_analyzer import EnhancedDataAnalyzer
from .data_flow_optimizer import DataFlowOptimizer
from .smart_scheduler import SmartScheduler, TaskPriority

logger = logging.getLogger(__name__)


class IntegratedAnalyzer:
    """集成数据分析器"""

    def __init__(
        self,
        ai_config="ai/ai_config.json",
        rag_config=None,
        enable_optimization=True,
        enable_scheduling=True,
        max_workers=3,
    ):
        self.logger = logging.getLogger(__name__)

        # 初始化核心组件
        self.enhanced_analyzer = EnhancedDataAnalyzer(
            ai_config=ai_config,
            rag_config=rag_config,
            enable_cache=True,
            enable_performance_tracking=True,
        )

        self.flow_optimizer = (
            DataFlowOptimizer(
                enable_metrics=enable_optimization,
                enable_quality_check=enable_optimization,
            )
            if enable_optimization
            else None
        )

        self.scheduler = (
            SmartScheduler(
                max_workers=max_workers, enable_system_monitor=enable_scheduling
            )
            if enable_scheduling
            else None
        )

        # 配置
        self.config = {
            "enable_optimization": enable_optimization,
            "enable_scheduling": enable_scheduling,
            "batch_threshold": 5,  # 批量处理阈值
            "auto_quality_check": True,
            "adaptive_processing": True,
        }

        # 统计信息
        self.stats = {
            "total_analyses": 0,
            "successful_analyses": 0,
            "failed_analyses": 0,
            "avg_processing_time": 0.0,
            "cache_hit_rate": 0.0,
            "quality_scores": [],
        }

        self.lock = threading.Lock()

        # 启动调度器
        if self.scheduler:
            self.scheduler.start()

        self.logger.info("集成数据分析器初始化完成")

    def analyze_single(
        self,
        report_text: str,
        department: Optional[str] = None,
        role: Optional[str] = None,
        priority: TaskPriority = TaskPriority.NORMAL,
        use_cache: bool = True,
        callback: Optional[Callable] = None,
    ) -> Dict[str, Any]:
        """
        分析单个报告

        Args:
            report_text: 报告文本
            department: 部门
            role: 岗位
            priority: 任务优先级
            use_cache: 是否使用缓存
            callback: 完成回调函数

        Returns:
            分析结果
        """
        start_time = time.time()

        try:
            # 如果启用调度器，提交任务
            if self.scheduler:
                task_id = f"single_{int(time.time() * 1000)}"
                task_data = {
                    "report_text": report_text,
                    "department": department,
                    "role": role,
                    "use_cache": use_cache,
                }

                # 同步等待结果
                result = self._submit_and_wait(task_id, task_data, priority, callback)
            else:
                # 直接分析
                result = self.enhanced_analyzer.analyze(
                    report_text, department, role, use_cache=use_cache
                )

            # 更新统计
            processing_time = time.time() - start_time
            self._update_stats(True, processing_time, result.get("quality_score", 0.0))

            self.logger.info(f"单个报告分析完成，耗时: {processing_time:.2f}秒")
            return result

        except Exception as e:
            processing_time = time.time() - start_time
            self._update_stats(False, processing_time)
            self.logger.error(f"单个报告分析失败: {e}")
            raise

    def analyze_batch(
        self,
        reports: List[Dict[str, Any]],
        use_optimization: Optional[bool] = None,
        use_parallel: bool = True,
        callback: Optional[Callable] = None,
    ) -> List[Dict[str, Any]]:
        """
        批量分析报告

        Args:
            reports: 报告列表，格式: [{'text': str, 'department': str, 'role': str}]
            use_optimization: 是否使用流程优化
            use_parallel: 是否并行处理
            callback: 完成回调函数

        Returns:
            分析结果列表
        """
        if not reports:
            return []

        start_time = time.time()
        use_optimization = (
            use_optimization
            if use_optimization is not None
            else self.config["enable_optimization"]
        )

        self.logger.info(f"开始批量分析 {len(reports)} 个报告")

        try:
            if use_optimization and self.flow_optimizer:
                # 使用流程优化器
                results = self.flow_optimizer.optimize_data_processing(
                    reports, self._process_single_report, use_parallel=use_parallel
                )
            else:
                # 直接批量处理
                if use_parallel:
                    results = self.enhanced_analyzer.batch_analyze(
                        reports, use_parallel=True
                    )
                else:
                    results = []
                    for report in reports:
                        result = self.enhanced_analyzer.analyze(
                            report["text"], report.get("department"), report.get("role")
                        )
                        results.append(result)

            # 统计处理
            processing_time = time.time() - start_time
            successful_count = sum(1 for r in results if "error" not in r)
            failed_count = len(results) - successful_count

            with self.lock:
                self.stats["total_analyses"] += len(reports)
                self.stats["successful_analyses"] += successful_count
                self.stats["failed_analyses"] += failed_count

                # 更新平均处理时间
                total_time = self.stats["avg_processing_time"] * (
                    self.stats["total_analyses"] - len(reports)
                )
                total_time += processing_time
                self.stats["avg_processing_time"] = (
                    total_time / self.stats["total_analyses"]
                )

            self.logger.info(
                f"批量分析完成，成功: {successful_count}, 失败: {failed_count}, 耗时: {processing_time:.2f}秒"
            )

            if callback:
                callback(results)

            return results

        except Exception as e:
            self.logger.error(f"批量分析失败: {e}")
            raise

    def _process_single_report(self, report: Dict[str, Any]) -> Dict[str, Any]:
        """处理单个报告（用于流程优化器）"""
        return self.enhanced_analyzer.analyze(
            report["text"], report.get("department"), report.get("role")
        )

    def _submit_and_wait(
        self,
        task_id: str,
        task_data: Dict[str, Any],
        priority: TaskPriority,
        callback: Optional[Callable],
    ) -> Dict[str, Any]:
        """提交任务并等待结果"""
        result_event = threading.Event()
        result_container = {"result": None, "error": None}

        def task_callback(task):
            if task.status.value == "completed":
                result_container["result"] = task.result
            else:
                result_container["error"] = task.error
            result_event.set()

        # 提交任务
        self.scheduler.submit_task(task_id, task_data, priority, task_callback)

        # 等待结果
        if result_event.wait(timeout=300):  # 5分钟超时
            if result_container["error"]:
                raise Exception(f"任务执行失败: {result_container['error']}")
            return result_container["result"]
        else:
            # 超时，取消任务
            self.scheduler.cancel_task(task_id)
            raise TimeoutError(f"任务执行超时: {task_id}")

    def _update_stats(
        self, success: bool, processing_time: float, quality_score: float = 0.0
    ):
        """更新统计信息"""
        with self.lock:
            self.stats["total_analyses"] += 1

            if success:
                self.stats["successful_analyses"] += 1
                if quality_score > 0:
                    self.stats["quality_scores"].append(quality_score)
            else:
                self.stats["failed_analyses"] += 1

            # 更新平均处理时间
            total_time = self.stats["avg_processing_time"] * (
                self.stats["total_analyses"] - 1
            )
            total_time += processing_time
            self.stats["avg_processing_time"] = (
                total_time / self.stats["total_analyses"]
            )

    def get_comprehensive_statistics(self) -> Dict[str, Any]:
        """获取综合统计信息"""
        stats = {}

        # 基础统计
        with self.lock:
            stats["basic_stats"] = self.stats.copy()

            # 计算成功率
            if self.stats["total_analyses"] > 0:
                stats["basic_stats"]["success_rate"] = (
                    self.stats["successful_analyses"] / self.stats["total_analyses"]
                )

            # 计算平均质量分
            if self.stats["quality_scores"]:
                stats["basic_stats"]["avg_quality_score"] = sum(
                    self.stats["quality_scores"]
                ) / len(self.stats["quality_scores"])

        # 增强分析器统计
        stats["analyzer_stats"] = self.enhanced_analyzer.get_performance_statistics()

        # 流程优化器统计
        if self.flow_optimizer:
            stats["optimizer_stats"] = self.flow_optimizer.get_optimization_statistics()

        # 调度器统计
        if self.scheduler:
            stats["scheduler_stats"] = self.scheduler.get_scheduler_statistics()

        return stats

    def optimize_performance(self) -> Dict[str, Any]:
        """性能优化建议"""
        stats = self.get_comprehensive_statistics()
        recommendations = []

        # 分析成功率
        success_rate = stats["basic_stats"].get("success_rate", 0)
        if success_rate < 0.9:
            recommendations.append("成功率较低，建议检查数据质量和模型配置")

        # 分析处理时间
        avg_time = stats["basic_stats"].get("avg_processing_time", 0)
        if avg_time > 10:
            recommendations.append("平均处理时间较长，建议启用缓存和并行处理")

        # 分析质量分
        avg_quality = stats["basic_stats"].get("avg_quality_score", 0)
        if avg_quality < 0.7:
            recommendations.append("分析质量较低，建议优化提示词和模型参数")

        # 系统负载建议
        if self.scheduler:
            scheduler_stats = stats.get("scheduler_stats", {})
            system_stats = scheduler_stats.get("system_stats", {})

            if system_stats.get("load_level") == "high":
                recommendations.append("系统负载较高，建议减少并发数或优化资源使用")

        return {
            "current_performance": stats["basic_stats"],
            "recommendations": recommendations,
            "optimization_actions": self._generate_optimization_actions(stats),
        }

    def _generate_optimization_actions(self, stats: Dict[str, Any]) -> List[str]:
        """生成优化操作建议"""
        actions = []

        # 缓存优化
        analyzer_stats = stats.get("analyzer_stats", {})
        cache_stats = analyzer_stats.get("cache_statistics", {})

        if cache_stats.get("cache_usage", 0) > 0.8:
            actions.append("清理分析缓存")

        # 模型优化
        model_performance = analyzer_stats.get("model_performance", {})
        if model_performance:
            best_model = analyzer_stats.get("best_model")
            if best_model:
                actions.append(f"切换到最佳模型: {best_model}")

        # 调度优化
        if self.scheduler:
            scheduler_stats = stats.get("scheduler_stats", {})
            queue_size = scheduler_stats.get("queue_size", 0)

            if queue_size > 20:
                actions.append("增加工作线程数")

        return actions

    def apply_optimization(self, action: str) -> bool:
        """应用优化操作"""
        try:
            if action == "清理分析缓存":
                self.enhanced_analyzer.clear_cache()
                return True

            elif action.startswith("切换到最佳模型"):
                # 这里可以实现模型切换逻辑
                self.logger.info(f"模型优化建议: {action}")
                return True

            elif action == "增加工作线程数":
                if self.scheduler:
                    # 这里可以实现动态调整工作线程数
                    self.logger.info("建议增加工作线程数")
                return True

            else:
                self.logger.warning(f"未知的优化操作: {action}")
                return False

        except Exception as e:
            self.logger.error(f"应用优化操作失败: {e}")
            return False

    def shutdown(self):
        """关闭分析器"""
        if self.scheduler:
            self.scheduler.stop()

        self.logger.info("集成数据分析器已关闭")

    def __enter__(self):
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        self.shutdown()
