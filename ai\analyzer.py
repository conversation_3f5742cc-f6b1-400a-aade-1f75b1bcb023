import os
import json
import logging
from .adapter import AIAdapter
from .schema import validate_and_fix, SchemaValidationError
from .prompt_loader import PromptLoader
from .tagger import assign_tags_to_report, generate_tags
from .anomaly_detector import detect_anomalies
from .analysis.rag_adapter import RAGAdapter

logger = logging.getLogger(__name__)


class AIAnalyzer:
    def __init__(self, ai_config="ai/ai_config.json", rag_config=None):
        self.adapter = AIAdapter(ai_config)
        self.prompt_loader = PromptLoader()
        self.rag_adapter = None

        # 初始化RAG适配器（如果配置了）
        if rag_config:
            try:
                self.rag_adapter = RAGAdapter(rag_config, logger)
                logger.info("RAG适配器初始化成功")
            except Exception as e:
                logger.error(f"RAG适配器初始化失败: {e}")

    def analyze(self, report_text, department=None, role=None, use_rag=True):
        """
        分析周报内容，支持多岗位适配和专项分析

        Args:
            report_text: 周报文本内容
            department: 部门，用于选择合适的模板
            role: 岗位，用于选择合适的模板
            use_rag: 是否使用RAG检索增强

        Returns:
            分析结果字典
        """
        # 1. 分流key生成
        key = f'{department or ""}{"-" if department and role else ""}{role or ""}.json'
        logger.info(f"使用模板: {key}")

        # 2. 加载模板
        prompt_cfg = self.prompt_loader.load(key)
        prompt = prompt_cfg["template"]

        # 3. RAG检索增强（如果启用）
        if use_rag and self.rag_adapter:
            try:
                # 提取查询关键词
                query = f"部门:{department} 岗位:{role} {report_text[:200]}"
                # 调用RAG检索
                rag_results = self.rag_adapter.query(query)

                if not isinstance(rag_results, dict) or "error" in rag_results:
                    logger.warning(f"RAG检索失败，使用原始提示词: {rag_results}")
                else:
                    # 将RAG结果添加到提示词中
                    context = "\n\n相关背景知识：\n"
                    if "knowledge" in rag_results and isinstance(
                        rag_results["knowledge"], list
                    ):
                        for i, item in enumerate(rag_results["knowledge"]):
                            if isinstance(item, dict) and "content" in item:
                                context += f"{i+1}. {item['content']}\n"

                    # 只有在成功获取知识时才增强提示词
                    if context != "\n\n相关背景知识：\n":
                        prompt = prompt.replace(
                            "{content}", f"{context}\n\n{{content}}"
                        )
                        logger.info("成功使用RAG增强提示词")
            except Exception as e:
                logger.error(f"RAG增强失败: {e}")

        # 4. 替换内容并调用AI
        prompt = prompt.replace("{content}", report_text)
        ai_output = self.adapter.call(prompt)

        try:
            result = json.loads(ai_output)
        except Exception as e:
            raise ValueError(f"AI输出非JSON: {e}\n原始输出: {ai_output}")

        # 5. Schema校验
        validate_and_fix(result)

        # 6. 标签分配 - 使用两种方式
        ai_tags = result.get("tags", [])
        generated_tags = generate_tags(result)
        keyword_tags = assign_tags_to_report(result)

        # 合并标签并去重
        all_tags = list(set(ai_tags + generated_tags + keyword_tags))
        result["tags"] = all_tags

        # 7. 异常检测
        result["anomaly_flags"] = detect_anomalies(result)

        # 8. 专项分析补全
        # 确保创新能力分析、品质分析、趋势分析字段存在
        for field in ["innovation_analysis", "quality_analysis", "trend_analysis"]:
            if field not in result or not result[field]:
                result[field] = ""

        # 9. 其他补全
        result["ai_version"] = prompt_cfg.get("ai_version", "auto")
        result["raw_text"] = report_text

        # 10. 员工信息补全
        if "employee" not in result or not isinstance(result["employee"], dict):
            result["employee"] = {}
        result["employee"].setdefault("name", prompt_cfg.get("name", ""))
        result["employee"].setdefault("email", prompt_cfg.get("email", ""))
        result["employee"].setdefault("department", department or "")
        result["employee"].setdefault("role", role or "")

        # 优先保留AI输出中的week字段，仅在缺失时用prompt_cfg补全
        if not result.get("week"):
            result["week"] = prompt_cfg.get("week", "")

        # 11. 指标计算补全
        metrics = result.get("metrics", {})
        if not isinstance(metrics, dict):
            metrics = {}
            result["metrics"] = metrics

        # 计算饱和度（如果未提供）
        if "saturation" not in metrics and "total_hours" in metrics:
            # 不同岗位的标准工时
            role_standard_hours = {
                "工程师": 40,
                "技术支持": 40,
                "销售": 35,
                "客服": 38,
                "管理": 45,
            }
            standard = role_standard_hours.get(role or "默认", 40)
            metrics["saturation"] = round(metrics["total_hours"] / standard, 2)

            # 饱和度标签
            if metrics["saturation"] > 1.2:
                metrics["saturation_tag"] = "过载"
            elif metrics["saturation"] > 0.9:
                metrics["saturation_tag"] = "饱和"
            elif metrics["saturation"] > 0.7:
                metrics["saturation_tag"] = "适中"
            else:
                metrics["saturation_tag"] = "不足"

        return result
