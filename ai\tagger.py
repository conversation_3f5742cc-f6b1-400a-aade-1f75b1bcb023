import os
import json
import re
from typing import List, Dict, Any, Optional

TAG_DICT_FILE = os.path.join(
    os.path.dirname(__file__), "../prompt_templates/tag_dictionary.json"
)


def load_tag_dictionary() -> Dict[str, dict]:
    """
    加载标签字典

    Returns:
        标签字典，键为标签名，值为标签信息
    """
    if os.path.exists(TAG_DICT_FILE):
        with open(TAG_DICT_FILE, "r", encoding="utf-8") as f:
            return {tag["tag"]: tag for tag in json.load(f)}
    return {}


def assign_tags_to_report(
    report: dict, tag_dict: Optional[Dict[str, dict]] = None
) -> List[str]:
    """
    根据结构化分析结果自动分配标签（按关键词匹配）

    Args:
        report: 分析结果字典
        tag_dict: 标签字典，如果为None则自动加载

    Returns:
        分配的标签列表
    """
    if tag_dict is None:
        tag_dict = load_tag_dictionary()

    tags = set()

    # 将报告转换为文本进行关键词匹配
    text = json.dumps(report, ensure_ascii=False).lower()

    # 按关键词匹配标签
    for tag, info in tag_dict.items():
        # 如果标签本身在文本中
        if tag.lower() in text:
            tags.add(tag)

        # 如果标签有关键词列表，检查关键词
        keywords = info.get("keywords", [])
        for keyword in keywords:
            if keyword.lower() in text:
                tags.add(tag)
                break

    return list(tags)


def generate_tags(analysis_result: dict) -> list:
    """
    根据分析结果生成标签（基于规则）

    Args:
        analysis_result: 分析结果字典

    Returns:
        生成的标签列表
    """
    tags = []

    # 获取指标数据
    metrics = analysis_result.get("metrics", {})
    if not isinstance(metrics, dict):
        metrics = {}

    # 1. 饱和度标签
    saturation = metrics.get("saturation")
    if saturation is not None:
        if saturation > 1.2:
            tags.append("过载")
        elif saturation > 0.9:
            tags.append("饱和")
        elif saturation > 0.7:
            tags.append("适中")
        else:
            tags.append("工作量不足")

    # 2. 工时标签
    total_hours = metrics.get("total_hours")
    if total_hours is not None:
        if total_hours > 45:
            tags.append("加班")
        elif total_hours < 30:
            tags.append("工时偏低")

    # 3. 创新标签
    innovation_analysis = analysis_result.get("innovation_analysis", "")
    if innovation_analysis:
        if isinstance(innovation_analysis, str) and len(innovation_analysis) > 10:
            # 检查创新关键词
            innovation_keywords = [
                "创新",
                "突破",
                "改进",
                "优化",
                "新方法",
                "新技术",
                "新思路",
            ]
            for keyword in innovation_keywords:
                if keyword in innovation_analysis:
                    tags.append("创新")
                    break
        elif isinstance(innovation_analysis, dict):
            innovation_score = innovation_analysis.get("score")
            if innovation_score and innovation_score > 0.7:
                tags.append("创新")

    # 4. 品质标签
    quality_analysis = analysis_result.get("quality_analysis", "")
    if quality_analysis:
        if isinstance(quality_analysis, str) and len(quality_analysis) > 10:
            # 检查品质关键词
            quality_keywords = ["质量问题", "缺陷", "投诉", "改进", "品质风险"]
            for keyword in quality_keywords:
                if keyword in quality_analysis:
                    tags.append("品质风险")
                    break
        elif isinstance(quality_analysis, dict):
            quality_issue_count = quality_analysis.get("issue_count")
            if quality_issue_count and quality_issue_count > 0:
                tags.append("品质风险")

    # 5. 任务类型标签
    work_items = analysis_result.get("work_items", [])
    if isinstance(work_items, list) and work_items:
        # 统计各类任务
        task_types = {}
        for item in work_items:
            if isinstance(item, dict):
                category = item.get("category", "").lower()
                if category:
                    task_types[category] = task_types.get(category, 0) + 1

        # 根据任务类型分配标签
        for task_type, count in task_types.items():
            # 开发相关
            if any(
                keyword in task_type for keyword in ["开发", "编码", "程序", "代码"]
            ):
                tags.append("开发")

            # 测试相关
            if any(keyword in task_type for keyword in ["测试", "质量", "qa"]):
                tags.append("测试")

            # 客户支持相关
            if any(
                keyword in task_type for keyword in ["客户", "支持", "服务", "现场"]
            ):
                tags.append("客户支持")

            # 会议相关
            if any(keyword in task_type for keyword in ["会议", "沟通", "讨论"]):
                if count >= 3:  # 如果会议较多
                    tags.append("会议较多")

            # 文档相关
            if any(keyword in task_type for keyword in ["文档", "报告", "总结"]):
                tags.append("文档")

    # 6. 岗位特定标签
    employee = analysis_result.get("employee", {})
    if isinstance(employee, dict):
        role = employee.get("role", "").lower()

        # 技术支持岗位
        if "技术支持" in role:
            # 检查是否有客户支持相关内容
            if "客户" in json.dumps(analysis_result, ensure_ascii=False).lower():
                tags.append("客户支持")

        # 开发岗位
        if any(keyword in role for keyword in ["开发", "工程师", "程序员"]):
            # 检查是否有技术难题
            if "难题" in json.dumps(analysis_result, ensure_ascii=False).lower():
                tags.append("技术难题")

        # 销售岗位
        if "销售" in role:
            # 检查是否有销售业绩
            if any(
                keyword in json.dumps(analysis_result, ensure_ascii=False).lower()
                for keyword in ["销售", "业绩", "客户", "拜访"]
            ):
                tags.append("销售")

    # 7. 趋势标签
    trend_analysis = analysis_result.get("trend_analysis", "")
    if trend_analysis:
        if isinstance(trend_analysis, str) and len(trend_analysis) > 10:
            # 检查趋势关键词
            if (
                "上升" in trend_analysis
                or "增加" in trend_analysis
                or "提高" in trend_analysis
            ):
                tags.append("上升趋势")
            elif (
                "下降" in trend_analysis
                or "减少" in trend_analysis
                or "降低" in trend_analysis
            ):
                tags.append("下降趋势")
            elif "稳定" in trend_analysis or "持平" in trend_analysis:
                tags.append("稳定趋势")

    return tags


def extract_department_tags(department: str) -> List[str]:
    """
    根据部门名称提取标签

    Args:
        department: 部门名称

    Returns:
        部门相关标签列表
    """
    if not department:
        return []

    tags = []
    department = department.lower()

    # 部门映射到标签
    department_tag_map = {
        "技术": "技术部门",
        "研发": "研发部门",
        "销售": "销售部门",
        "市场": "市场部门",
        "客服": "客服部门",
        "支持": "支持部门",
        "产品": "产品部门",
        "设计": "设计部门",
        "测试": "测试部门",
        "质量": "质量部门",
        "运维": "运维部门",
        "人力": "人力资源",
        "财务": "财务部门",
        "行政": "行政部门",
    }

    for key, tag in department_tag_map.items():
        if key in department:
            tags.append(tag)

    return tags
