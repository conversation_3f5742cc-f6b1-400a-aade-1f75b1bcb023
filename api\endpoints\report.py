from fastapi import APIRouter, HTTPException, Query, Body
from pydantic import BaseModel, Field
from typing import List, Optional, Any
import logging
import db.orm as orm
from ai.analyzer import AIAnalyzer
from ai.analysis.integrated_analyzer import IntegratedAnalyzer
from ai.analysis.smart_scheduler import TaskPriority

router = APIRouter()
ai_analyzer = AIAnalyzer()
logger = logging.getLogger(__name__)

# 初始化增强分析器
try:
    integrated_analyzer = IntegratedAnalyzer(
        ai_config="ai/ai_config.json",
        enable_optimization=True,
        enable_scheduling=True,
        max_workers=3,
    )
    logger.info("增强分析器初始化成功")
except Exception as e:
    logger.error(f"增强分析器初始化失败: {e}")
    integrated_analyzer = None


# Pydantic模型严格对齐Schema
class Employee(BaseModel):
    name: str
    email: str
    department: str
    role: str


class WorkItem(BaseModel):
    title: str
    description: str
    duration_hours: float
    complexity: str
    category: str
    date: str


class Summary(BaseModel):
    total_hours: float
    saturation: float
    main_achievements: str
    risks: str
    suggestions: str


class WeeklyReport(BaseModel):
    report_id: str
    employee: Employee
    week: str
    work_items: List[WorkItem]
    summary: Summary
    metrics: dict = Field(default_factory=dict)
    ai_version: str
    raw_text: str
    tags: List[str] = Field(default_factory=list)
    anomaly_flags: List[Any] = Field(default_factory=list)


class AnalyzeRequest(BaseModel):
    report_text: str
    department: Optional[str] = None
    role: Optional[str] = None


@router.post("/analyze")
def analyze_report(req: AnalyzeRequest):
    try:
        result = ai_analyzer.analyze(req.report_text, req.department, req.role)
        # 数据入库
        orm.save_report_analysis(result)
        return {"success": True, "data": result}
    except Exception as e:
        raise HTTPException(status_code=400, detail=str(e))


@router.get("/query")
def query_reports(
    email: Optional[str] = None,
    department: Optional[str] = None,
    week: Optional[str] = None,
    tag: Optional[str] = None,
    anomaly: Optional[str] = None,
):
    # 多条件查询，支持标签、异常、趋势等过滤
    results = orm.query_report_analysis(
        email=email, department=department, week=week, tag=tag, anomaly=anomaly
    )
    return {"success": True, "data": results}


# 2. 查询周报分析结果（支持多条件、标签、异常、趋势过滤）
@router.get("/query", response_model=List[WeeklyReport])
def query_reports_old(
    email: Optional[str] = Query(None),
    department: Optional[str] = Query(None),
    week: Optional[str] = Query(None),
    tag: Optional[str] = Query(None),
    anomaly: Optional[str] = Query(None),
    limit: int = 50,
):
    # 伪实现：返回空列表
    logging.info(
        f"[API] 查询周报: email={email}, department={department}, week={week}, tag={tag}, anomaly={anomaly}"
    )
    # TODO: 实际数据库查询
    return []


# 增强分析端点
class EnhancedAnalyzeRequest(BaseModel):
    """增强分析请求模型"""

    report_text: str = Field(..., description="报告文本内容")
    department: Optional[str] = Field(None, description="部门")
    role: Optional[str] = Field(None, description="岗位")
    priority: str = Field("normal", description="任务优先级: urgent, high, normal, low")
    use_cache: bool = Field(True, description="是否使用缓存")


class BatchAnalyzeRequest(BaseModel):
    """批量分析请求模型"""

    reports: List[dict] = Field(..., description="报告列表")
    use_optimization: bool = Field(True, description="是否使用流程优化")
    use_parallel: bool = Field(True, description="是否并行处理")


@router.post("/analyze/enhanced")
async def analyze_report_enhanced(req: EnhancedAnalyzeRequest):
    """增强版报告分析"""
    if not integrated_analyzer:
        raise HTTPException(status_code=503, detail="增强分析器未初始化")

    try:
        # 转换优先级
        priority_map = {
            "urgent": TaskPriority.URGENT,
            "high": TaskPriority.HIGH,
            "normal": TaskPriority.NORMAL,
            "low": TaskPriority.LOW,
        }
        priority = priority_map.get(req.priority.lower(), TaskPriority.NORMAL)

        # 执行增强分析
        result = integrated_analyzer.analyze_single(
            report_text=req.report_text,
            department=req.department,
            role=req.role,
            priority=priority,
            use_cache=req.use_cache,
        )

        # 数据入库
        try:
            orm.save_report_analysis(result)
        except Exception as e:
            logger.warning(f"数据入库失败: {e}")

        return {
            "success": True,
            "data": result,
            "analysis_info": {
                "quality_score": result.get("quality_score", 0.0),
                "analysis_timestamp": result.get("analysis_timestamp"),
                "cache_used": req.use_cache,
            },
        }

    except Exception as e:
        logger.error(f"增强分析失败: {e}")
        raise HTTPException(status_code=500, detail=f"分析失败: {str(e)}")


@router.post("/analyze/batch")
async def analyze_reports_batch(req: BatchAnalyzeRequest):
    """批量报告分析"""
    if not integrated_analyzer:
        raise HTTPException(status_code=503, detail="增强分析器未初始化")

    if not req.reports:
        raise HTTPException(status_code=400, detail="报告列表不能为空")

    try:
        # 执行批量分析
        results = integrated_analyzer.analyze_batch(
            reports=req.reports,
            use_optimization=req.use_optimization,
            use_parallel=req.use_parallel,
        )

        # 统计结果
        successful_count = sum(1 for r in results if "error" not in r)
        failed_count = len(results) - successful_count

        # 批量入库成功的结果
        saved_count = 0
        for result in results:
            if "error" not in result:
                try:
                    orm.save_report_analysis(result)
                    saved_count += 1
                except Exception as e:
                    logger.warning(f"批量入库失败: {e}")

        return {
            "success": True,
            "data": results,
            "batch_info": {
                "total_reports": len(req.reports),
                "successful_analyses": successful_count,
                "failed_analyses": failed_count,
                "saved_to_db": saved_count,
                "optimization_used": req.use_optimization,
                "parallel_processing": req.use_parallel,
            },
        }

    except Exception as e:
        logger.error(f"批量分析失败: {e}")
        raise HTTPException(status_code=500, detail=f"批量分析失败: {str(e)}")


@router.get("/analyze/statistics")
async def get_analysis_statistics():
    """获取分析统计信息"""
    if not integrated_analyzer:
        raise HTTPException(status_code=503, detail="增强分析器未初始化")

    try:
        stats = integrated_analyzer.get_comprehensive_statistics()
        return {"success": True, "data": stats}
    except Exception as e:
        logger.error(f"获取统计信息失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取统计信息失败: {str(e)}")


@router.get("/analyze/performance")
async def get_performance_optimization():
    """获取性能优化建议"""
    if not integrated_analyzer:
        raise HTTPException(status_code=503, detail="增强分析器未初始化")

    try:
        optimization = integrated_analyzer.optimize_performance()
        return {"success": True, "data": optimization}
    except Exception as e:
        logger.error(f"获取性能优化建议失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取性能优化建议失败: {str(e)}")


@router.post("/analyze/optimize")
async def apply_optimization(action: str = Body(..., embed=True)):
    """应用优化操作"""
    if not integrated_analyzer:
        raise HTTPException(status_code=503, detail="增强分析器未初始化")

    try:
        success = integrated_analyzer.apply_optimization(action)
        return {
            "success": success,
            "message": f"优化操作{'成功' if success else '失败'}: {action}",
        }
    except Exception as e:
        logger.error(f"应用优化操作失败: {e}")
        raise HTTPException(status_code=500, detail=f"应用优化操作失败: {str(e)}")


@router.delete("/analyze/cache")
async def clear_analysis_cache():
    """清空分析缓存"""
    if not integrated_analyzer:
        raise HTTPException(status_code=503, detail="增强分析器未初始化")

    try:
        integrated_analyzer.enhanced_analyzer.clear_cache()
        return {"success": True, "message": "分析缓存已清空"}
    except Exception as e:
        logger.error(f"清空缓存失败: {e}")
        raise HTTPException(status_code=500, detail=f"清空缓存失败: {str(e)}")
