#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
装饰器修复脚本
修复 @performance_monitor 装饰器的使用方式
"""

import os
import re
from pathlib import Path

def fix_performance_monitor_decorators():
    """修复 @performance_monitor 装饰器"""
    
    # 需要修复的文件列表
    files_to_fix = [
        "services/data/data_service.py",
        "services/repositories/employee_repository.py", 
        "services/repositories/report_repository.py",
        "services/visualization/plotly_visualizer.py",
        "services/visualization/visualization_service.py"
    ]
    
    for file_path in files_to_fix:
        if not os.path.exists(file_path):
            print(f"⚠️ 文件不存在: {file_path}")
            continue
            
        print(f"🔧 修复文件: {file_path}")
        
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 修复 @performance_monitor 装饰器
        # 匹配单独的 @performance_monitor
        pattern1 = r'@performance_monitor\n'
        replacement1 = '@performance_monitor(log_execution_time=True)\n'
        content = re.sub(pattern1, replacement1, content)
        
        # 保存修复后的文件
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write(content)
        
        print(f"✅ 修复完成: {file_path}")

def fix_optional_parameters():
    """修复可选参数类型注解"""
    
    files_to_fix = [
        "ai/simple_analyzer.py",
        "ai/tagger.py", 
        "ai/analysis/enhanced_analyzer.py",
        "services/visualization/chart_factory.py",
        "services/visualization/dashboard_builder.py",
        "services/visualization/visualization_service.py"
    ]
    
    for file_path in files_to_fix:
        if not os.path.exists(file_path):
            print(f"⚠️ 文件不存在: {file_path}")
            continue
            
        print(f"🔧 修复可选参数: {file_path}")
        
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 修复常见的可选参数类型注解
        patterns = [
            (r'(\w+): str = None', r'\1: Optional[str] = None'),
            (r'(\w+): int = None', r'\1: Optional[int] = None'),
            (r'(\w+): Dict\[str, Any\] = None', r'\1: Optional[Dict[str, Any]] = None'),
            (r'(\w+): List\[\w+\] = None', r'\1: Optional[List[\w+]] = None'),
        ]
        
        for pattern, replacement in patterns:
            content = re.sub(pattern, replacement, content)
        
        # 确保导入了 Optional
        if 'Optional' in content and 'from typing import' in content:
            # 检查是否已经导入了 Optional
            if 'Optional' not in content.split('from typing import')[1].split('\n')[0]:
                content = content.replace(
                    'from typing import',
                    'from typing import Optional,'
                )
        
        # 保存修复后的文件
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write(content)
        
        print(f"✅ 修复完成: {file_path}")

def main():
    """主函数"""
    print("🚀 开始修复装饰器和类型注解问题")
    print("=" * 50)
    
    fix_performance_monitor_decorators()
    print()
    
    fix_optional_parameters()
    print()
    
    print("=" * 50)
    print("🎉 修复完成！")

if __name__ == "__main__":
    main()
