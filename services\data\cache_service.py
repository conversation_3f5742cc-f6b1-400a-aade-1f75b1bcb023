#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
缓存服务

模块描述: 提供统一的缓存管理功能，支持多种缓存策略
作者: 开发团队
创建时间: 2025-01-27
版本: 1.0.0
依赖: core.interfaces, core.base
"""

from typing import Any, Optional, Dict, List
from datetime import datetime, timedelta
import json
import hashlib
import threading
import time

from core.interfaces import ICacheManager
from core.base import BaseService
from core.decorators import performance_monitor, error_handler
from core.exceptions import CacheError


class CacheService(BaseService, ICacheManager):
    """
    缓存服务

    功能：
    - 内存缓存
    - 分布式缓存（Redis）
    - 缓存策略管理
    - 缓存统计和监控
    """

    def __init__(self, config, redis_client=None):
        """
        初始化缓存服务

        Args:
            config: 服务配置
            redis_client: Redis客户端（可选）
        """
        super().__init__(config)
        self.redis_client = redis_client

        # 内存缓存
        self._memory_cache: Dict[str, Dict[str, Any]] = {}
        self._cache_lock = threading.RLock()

        # 缓存统计
        self._stats = {"hits": 0, "misses": 0, "sets": 0, "deletes": 0, "evictions": 0}

        # 缓存配置
        self.default_ttl = getattr(config, "default_ttl", 3600)
        self.max_memory_items = getattr(config, "max_memory_items", 10000)
        self.cleanup_interval = getattr(config, "cleanup_interval", 300)  # 5分钟

        # 启动清理线程
        self._cleanup_thread = None
        self._stop_cleanup = False

    def initialize(self) -> bool:
        """初始化服务"""
        try:
            # 测试Redis连接（如果配置了）
            if self.redis_client:
                try:
                    self.redis_client.ping()
                    self.logger.info("Redis连接测试成功")
                except Exception as e:
                    self.logger.warning(f"Redis连接失败，将使用内存缓存: {e}")
                    self.redis_client = None

            # 启动清理线程
            self._start_cleanup_thread()

            self.logger.info("缓存服务初始化完成")
            return True
        except Exception as e:
            self.logger.error(f"缓存服务初始化失败: {e}")
            return False

    def validate_config(self) -> bool:
        """验证配置"""
        return True

    def get_service_info(self) -> Dict[str, Any]:
        """获取服务信息"""
        return {
            "service_name": self.config.name,
            "version": self.config.version,
            "redis_enabled": self.redis_client is not None,
            "memory_cache_size": len(self._memory_cache),
            "max_memory_items": self.max_memory_items,
            "default_ttl": self.default_ttl,
            "stats": self._stats.copy(),
        }

    @performance_monitor(log_execution_time=True)
    @error_handler(default_return=None)
    def get(self, key: str) -> Optional[Any]:
        """
        获取缓存

        Args:
            key: 缓存键

        Returns:
            Optional[Any]: 缓存值，如果不存在则返回None
        """
        # 先尝试从Redis获取
        if self.redis_client:
            try:
                value = self._get_from_redis(key)
                if value is not None:
                    self._stats["hits"] += 1
                    return value
            except Exception as e:
                self.logger.warning(f"从Redis获取缓存失败: {key}, 错误: {e}")

        # 从内存缓存获取
        with self._cache_lock:
            if key in self._memory_cache:
                cache_item = self._memory_cache[key]

                # 检查是否过期
                if self._is_expired(cache_item):
                    del self._memory_cache[key]
                    self._stats["evictions"] += 1
                    self._stats["misses"] += 1
                    return None

                self._stats["hits"] += 1
                return cache_item["value"]

        self._stats["misses"] += 1
        return None

    @performance_monitor(log_execution_time=True)
    @error_handler(default_return=False)
    def set(self, key: str, value: Any, ttl: Optional[int] = None) -> bool:
        """
        设置缓存

        Args:
            key: 缓存键
            value: 缓存值
            ttl: 过期时间（秒），None表示使用默认TTL

        Returns:
            bool: 设置是否成功
        """
        if ttl is None:
            ttl = self.default_ttl

        expiry_time = datetime.now() + timedelta(seconds=ttl)

        # 设置到Redis
        if self.redis_client:
            try:
                self._set_to_redis(key, value, ttl)
            except Exception as e:
                self.logger.warning(f"设置Redis缓存失败: {key}, 错误: {e}")

        # 设置到内存缓存
        with self._cache_lock:
            # 检查内存缓存大小限制
            if len(self._memory_cache) >= self.max_memory_items:
                self._evict_lru_items()

            self._memory_cache[key] = {
                "value": value,
                "expiry_time": expiry_time,
                "access_time": datetime.now(),
            }

        self._stats["sets"] += 1
        return True

    @performance_monitor(log_execution_time=True)
    @error_handler(default_return=False)
    def delete(self, key: str) -> bool:
        """
        删除缓存

        Args:
            key: 缓存键

        Returns:
            bool: 删除是否成功
        """
        success = True

        # 从Redis删除
        if self.redis_client:
            try:
                self.redis_client.delete(key)
            except Exception as e:
                self.logger.warning(f"从Redis删除缓存失败: {key}, 错误: {e}")
                success = False

        # 从内存缓存删除
        with self._cache_lock:
            if key in self._memory_cache:
                del self._memory_cache[key]
                self._stats["deletes"] += 1
            else:
                success = False

        return success

    @performance_monitor(log_execution_time=True)
    def exists(self, key: str) -> bool:
        """
        检查缓存是否存在

        Args:
            key: 缓存键

        Returns:
            bool: 缓存是否存在
        """
        # 检查Redis
        if self.redis_client:
            try:
                if self.redis_client.exists(key):
                    return True
            except Exception as e:
                self.logger.warning(f"检查Redis缓存存在性失败: {key}, 错误: {e}")

        # 检查内存缓存
        with self._cache_lock:
            if key in self._memory_cache:
                cache_item = self._memory_cache[key]
                if not self._is_expired(cache_item):
                    return True
                else:
                    # 清理过期项
                    del self._memory_cache[key]
                    self._stats["evictions"] += 1

        return False

    @performance_monitor(log_execution_time=True)
    @error_handler(default_return=False)
    def clear(self) -> bool:
        """
        清空所有缓存

        Returns:
            bool: 清空是否成功
        """
        success = True

        # 清空Redis
        if self.redis_client:
            try:
                self.redis_client.flushdb()
            except Exception as e:
                self.logger.warning(f"清空Redis缓存失败: {e}")
                success = False

        # 清空内存缓存
        with self._cache_lock:
            self._memory_cache.clear()

        # 重置统计
        self._stats = {"hits": 0, "misses": 0, "sets": 0, "deletes": 0, "evictions": 0}

        return success

    def get_stats(self) -> Dict[str, Any]:
        """
        获取缓存统计信息

        Returns:
            Dict[str, Any]: 统计信息
        """
        with self._cache_lock:
            memory_stats = {
                "memory_cache_size": len(self._memory_cache),
                "memory_cache_max": self.max_memory_items,
            }

        redis_stats = {}
        if self.redis_client:
            try:
                redis_info = self.redis_client.info()
                redis_stats = {
                    "redis_used_memory": redis_info.get("used_memory_human", "N/A"),
                    "redis_connected_clients": redis_info.get("connected_clients", 0),
                    "redis_keyspace_hits": redis_info.get("keyspace_hits", 0),
                    "redis_keyspace_misses": redis_info.get("keyspace_misses", 0),
                }
            except Exception as e:
                redis_stats = {"redis_error": str(e)}

        # 计算命中率
        total_requests = self._stats["hits"] + self._stats["misses"]
        hit_rate = (
            (self._stats["hits"] / total_requests * 100) if total_requests > 0 else 0
        )

        return {
            **self._stats,
            "hit_rate_percent": round(hit_rate, 2),
            **memory_stats,
            **redis_stats,
        }

    def get_cache_keys(self, pattern: str = "*") -> List[str]:
        """
        获取缓存键列表

        Args:
            pattern: 匹配模式

        Returns:
            List[str]: 缓存键列表
        """
        keys = []

        # 从Redis获取
        if self.redis_client:
            try:
                redis_keys = self.redis_client.keys(pattern)
                keys.extend(
                    [
                        key.decode() if isinstance(key, bytes) else key
                        for key in redis_keys
                    ]
                )
            except Exception as e:
                self.logger.warning(f"从Redis获取键列表失败: {e}")

        # 从内存缓存获取
        with self._cache_lock:
            memory_keys = list(self._memory_cache.keys())
            # 简单的模式匹配
            if pattern != "*":
                import fnmatch

                memory_keys = [
                    key for key in memory_keys if fnmatch.fnmatch(key, pattern)
                ]
            keys.extend(memory_keys)

        return list(set(keys))  # 去重

    def _get_from_redis(self, key: str) -> Optional[Any]:
        """从Redis获取缓存"""
        if not self.redis_client:
            return None

        try:
            value = self.redis_client.get(key)
            if value is None:
                return None

            # 反序列化
            return json.loads(value)
        except Exception as e:
            raise CacheError(f"从Redis获取缓存失败: {e}")

    def _set_to_redis(self, key: str, value: Any, ttl: int) -> None:
        """设置缓存到Redis"""
        if not self.redis_client:
            return

        try:
            # 序列化
            serialized_value = json.dumps(value, default=str)
            self.redis_client.setex(key, ttl, serialized_value)
        except Exception as e:
            raise CacheError(f"设置Redis缓存失败: {e}")

    def _is_expired(self, cache_item: Dict[str, Any]) -> bool:
        """检查缓存项是否过期"""
        return datetime.now() > cache_item["expiry_time"]

    def _evict_lru_items(self, count: Optional[int] = None) -> None:
        """清理最近最少使用的缓存项"""
        if count is None:
            count = max(1, len(self._memory_cache) // 10)  # 清理10%

        # 按访问时间排序
        items = list(self._memory_cache.items())
        items.sort(key=lambda x: x[1]["access_time"])

        # 删除最旧的项
        for i in range(min(count, len(items))):
            key = items[i][0]
            del self._memory_cache[key]
            self._stats["evictions"] += 1

    def _cleanup_expired_items(self) -> None:
        """清理过期的缓存项"""
        with self._cache_lock:
            expired_keys = []
            for key, cache_item in self._memory_cache.items():
                if self._is_expired(cache_item):
                    expired_keys.append(key)

            for key in expired_keys:
                del self._memory_cache[key]
                self._stats["evictions"] += 1

            if expired_keys:
                self.logger.debug(f"清理了 {len(expired_keys)} 个过期缓存项")

    def _start_cleanup_thread(self) -> None:
        """启动清理线程"""

        def cleanup_worker():
            while not self._stop_cleanup:
                try:
                    self._cleanup_expired_items()
                    time.sleep(self.cleanup_interval)
                except Exception as e:
                    self.logger.error(f"缓存清理线程错误: {e}")
                    time.sleep(60)  # 出错时等待1分钟再重试

        self._cleanup_thread = threading.Thread(target=cleanup_worker, daemon=True)
        self._cleanup_thread.start()
        self.logger.info("缓存清理线程已启动")

    def _cleanup(self) -> None:
        """清理资源"""
        # 停止清理线程
        self._stop_cleanup = True
        if self._cleanup_thread and self._cleanup_thread.is_alive():
            self._cleanup_thread.join(timeout=5)

        # 清理缓存
        with self._cache_lock:
            self._memory_cache.clear()

        # 关闭Redis连接
        if self.redis_client:
            try:
                self.redis_client.close()
            except Exception as e:
                self.logger.error(f"关闭Redis连接失败: {e}")

        self.logger.info("缓存服务资源清理完成")
