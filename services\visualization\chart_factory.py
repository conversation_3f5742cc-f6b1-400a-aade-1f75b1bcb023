#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
图表工厂

模块描述: 图表创建工厂，提供预定义的图表模板和快速创建方法
作者: 开发团队
创建时间: 2025-01-27
版本: 1.0.0
依赖: .plotly_visualizer, domain.entities, domain.value_objects
"""

from typing import Dict, Any, List, Optional, Union
import pandas as pd
from datetime import datetime

from .plotly_visualizer import PlotlyVisualizer
from domain.entities import WeeklyReport, AnalysisResult
from domain.value_objects import VisualizationConfig


class ChartFactory:
    """
    图表工厂

    提供预定义的图表模板和快速创建方法：
    - 工作量分析图表
    - 任务分布图表
    - 趋势分析图表
    - 对比分析图表
    - 性能指标图表
    """

    def __init__(self, visualizer: Optional[PlotlyVisualizer] = None):
        """
        初始化图表工厂

        Args:
            visualizer: 可视化器实例
        """
        self.visualizer = visualizer or PlotlyVisualizer()

        # 预定义的图表模板
        self.chart_templates = {
            "workload_trend": self._create_workload_trend_template,
            "task_distribution": self._create_task_distribution_template,
            "complexity_analysis": self._create_complexity_analysis_template,
            "department_comparison": self._create_department_comparison_template,
            "efficiency_metrics": self._create_efficiency_metrics_template,
            "time_analysis": self._create_time_analysis_template,
            "employee_performance": self._create_employee_performance_template,
            "weekly_summary": self._create_weekly_summary_template,
        }

        # 默认颜色方案
        self.color_schemes = {
            "default": ["#1f77b4", "#ff7f0e", "#2ca02c", "#d62728", "#9467bd"],
            "professional": ["#2E86AB", "#A23B72", "#F18F01", "#C73E1D", "#592E83"],
            "pastel": ["#FFB3BA", "#FFDFBA", "#FFFFBA", "#BAFFC9", "#BAE1FF"],
            "dark": ["#2C3E50", "#E74C3C", "#3498DB", "#F39C12", "#9B59B6"],
        }

    def create_chart_from_template(
        self,
        template_name: str,
        data: Any,
        config_overrides: Optional[Dict[str, Any]] = None,
    ) -> Any:
        """
        从模板创建图表

        Args:
            template_name: 模板名称
            data: 数据
            config_overrides: 配置覆盖

        Returns:
            Any: 图表对象
        """
        if template_name not in self.chart_templates:
            raise ValueError(f"未知的图表模板: {template_name}")

        template_func = self.chart_templates[template_name]
        config = template_func(data, config_overrides or {})

        return self.visualizer.create_visualization(config["data"], config["config"])

    def create_workload_trend_chart(
        self, reports: List[WeeklyReport], config_overrides: Dict[str, Any] = None
    ) -> Any:
        """创建工作量趋势图"""
        return self.create_chart_from_template(
            "workload_trend", reports, config_overrides
        )

    def create_task_distribution_chart(
        self, reports: List[WeeklyReport], config_overrides: Dict[str, Any] = None
    ) -> Any:
        """创建任务分布图"""
        return self.create_chart_from_template(
            "task_distribution", reports, config_overrides
        )

    def create_complexity_analysis_chart(
        self, reports: List[WeeklyReport], config_overrides: Dict[str, Any] = None
    ) -> Any:
        """创建复杂度分析图"""
        return self.create_chart_from_template(
            "complexity_analysis", reports, config_overrides
        )

    def create_department_comparison_chart(
        self, reports: List[WeeklyReport], config_overrides: Dict[str, Any] = None
    ) -> Any:
        """创建部门对比图"""
        return self.create_chart_from_template(
            "department_comparison", reports, config_overrides
        )

    def create_efficiency_metrics_chart(
        self,
        analysis_results: List[AnalysisResult],
        config_overrides: Dict[str, Any] = None,
    ) -> Any:
        """创建效率指标图"""
        return self.create_chart_from_template(
            "efficiency_metrics", analysis_results, config_overrides
        )

    def create_dashboard_charts(
        self, reports: List[WeeklyReport], analysis_results: List[AnalysisResult] = None
    ) -> Dict[str, Any]:
        """
        创建仪表盘图表集合

        Args:
            reports: 周报列表
            analysis_results: 分析结果列表

        Returns:
            Dict[str, Any]: 图表集合
        """
        charts = {}

        try:
            # 工作量趋势图
            charts["workload_trend"] = self.create_workload_trend_chart(reports)
        except Exception as e:
            charts["workload_trend"] = {"error": str(e)}

        try:
            # 任务分布图
            charts["task_distribution"] = self.create_task_distribution_chart(reports)
        except Exception as e:
            charts["task_distribution"] = {"error": str(e)}

        try:
            # 复杂度分析图
            charts["complexity_analysis"] = self.create_complexity_analysis_chart(
                reports
            )
        except Exception as e:
            charts["complexity_analysis"] = {"error": str(e)}

        try:
            # 部门对比图
            charts["department_comparison"] = self.create_department_comparison_chart(
                reports
            )
        except Exception as e:
            charts["department_comparison"] = {"error": str(e)}

        # 如果有分析结果，添加分析图表
        if analysis_results:
            try:
                charts["efficiency_metrics"] = self.create_efficiency_metrics_chart(
                    analysis_results
                )
            except Exception as e:
                charts["efficiency_metrics"] = {"error": str(e)}

        return charts

    def _create_workload_trend_template(
        self, reports: List[WeeklyReport], config_overrides: Dict[str, Any]
    ) -> Dict[str, Any]:
        """创建工作量趋势模板"""
        # 提取数据
        data_points = []
        for report in reports:
            total_hours = sum(item.duration_hours for item in report.work_items)
            data_points.append(
                {
                    "x": report.week,
                    "y": total_hours,
                    "employee": report.employee.name,
                    "department": report.employee.department,
                    "task_count": len(report.work_items),
                }
            )

        # 按周聚合数据
        df = pd.DataFrame(data_points)
        if not df.empty:
            weekly_data = (
                df.groupby("x")
                .agg({"y": "mean", "task_count": "mean", "employee": "count"})
                .reset_index()
            )
            weekly_data.columns = ["x", "y", "avg_tasks", "employee_count"]
        else:
            weekly_data = pd.DataFrame(
                columns=["x", "y", "avg_tasks", "employee_count"]
            )

        # 默认配置
        default_config = VisualizationConfig(
            chart_type="line",
            title="工作量趋势分析",
            width=800,
            height=400,
            color_scheme="professional",
        )

        # 应用覆盖配置
        config_dict = default_config.__dict__.copy()
        config_dict.update(config_overrides)
        config = VisualizationConfig(**config_dict)

        return {"data": weekly_data, "config": config}

    def _create_task_distribution_template(
        self, reports: List[WeeklyReport], config_overrides: Dict[str, Any]
    ) -> Dict[str, Any]:
        """创建任务分布模板"""
        # 统计任务类别
        category_counts = {}
        for report in reports:
            for item in report.work_items:
                category = item.category.value
                category_counts[category] = category_counts.get(category, 0) + 1

        # 转换为DataFrame
        data = pd.DataFrame(
            [
                {"labels": category, "values": count}
                for category, count in category_counts.items()
            ]
        )

        # 默认配置
        default_config = VisualizationConfig(
            chart_type="pie",
            title="任务类别分布",
            width=600,
            height=400,
            color_scheme="default",
        )

        # 应用覆盖配置
        config_dict = default_config.__dict__.copy()
        config_dict.update(config_overrides)
        config = VisualizationConfig(**config_dict)

        return {"data": data, "config": config}

    def _create_complexity_analysis_template(
        self, reports: List[WeeklyReport], config_overrides: Dict[str, Any]
    ) -> Dict[str, Any]:
        """创建复杂度分析模板"""
        # 提取复杂度数据
        complexity_data = []
        for report in reports:
            complexity_dist = report.get_complexity_distribution()
            complexity_data.append(
                {
                    "x": f"{report.employee.name}\n({report.week})",
                    "low": complexity_dist.get("低", 0),
                    "medium": complexity_dist.get("中", 0),
                    "high": complexity_dist.get("高", 0),
                }
            )

        data = pd.DataFrame(complexity_data)

        # 默认配置
        default_config = VisualizationConfig(
            chart_type="bar",
            title="任务复杂度分析",
            width=800,
            height=500,
            color_scheme="professional",
        )

        # 应用覆盖配置
        config_dict = default_config.__dict__.copy()
        config_dict.update(config_overrides)
        config = VisualizationConfig(**config_dict)

        return {"data": data, "config": config}

    def _create_department_comparison_template(
        self, reports: List[WeeklyReport], config_overrides: Dict[str, Any]
    ) -> Dict[str, Any]:
        """创建部门对比模板"""
        # 按部门统计
        dept_stats = {}
        for report in reports:
            dept = report.employee.department
            if dept not in dept_stats:
                dept_stats[dept] = {
                    "total_hours": 0,
                    "total_tasks": 0,
                    "employees": set(),
                }

            dept_stats[dept]["total_hours"] += sum(
                item.duration_hours for item in report.work_items
            )
            dept_stats[dept]["total_tasks"] += len(report.work_items)
            dept_stats[dept]["employees"].add(report.employee.email)

        # 转换为DataFrame
        data_points = []
        for dept, stats in dept_stats.items():
            employee_count = len(stats["employees"])
            data_points.append(
                {
                    "x": dept,
                    "y": (
                        stats["total_hours"] / employee_count
                        if employee_count > 0
                        else 0
                    ),
                    "avg_tasks": (
                        stats["total_tasks"] / employee_count
                        if employee_count > 0
                        else 0
                    ),
                    "employee_count": employee_count,
                }
            )

        data = pd.DataFrame(data_points)

        # 默认配置
        default_config = VisualizationConfig(
            chart_type="bar",
            title="部门工作量对比",
            width=800,
            height=500,
            color_scheme="professional",
        )

        # 应用覆盖配置
        config_dict = default_config.__dict__.copy()
        config_dict.update(config_overrides)
        config = VisualizationConfig(**config_dict)

        return {"data": data, "config": config}

    def _create_efficiency_metrics_template(
        self, analysis_results: List[AnalysisResult], config_overrides: Dict[str, Any]
    ) -> Dict[str, Any]:
        """创建效率指标模板"""
        # 提取效率数据
        efficiency_data = []
        for result in analysis_results:
            if "efficiency" in result.result_data:
                efficiency_info = result.result_data["efficiency"]
                efficiency_data.append(
                    {
                        "x": result.report_id,
                        "y": efficiency_info.get("score", 0),
                        "confidence": result.confidence_score,
                        "processing_time": result.processing_time,
                    }
                )

        data = pd.DataFrame(efficiency_data)

        # 默认配置
        default_config = VisualizationConfig(
            chart_type="scatter",
            title="效率指标分析",
            width=800,
            height=400,
            color_scheme="professional",
        )

        # 应用覆盖配置
        config_dict = default_config.__dict__.copy()
        config_dict.update(config_overrides)
        config = VisualizationConfig(**config_dict)

        return {"data": data, "config": config}

    def _create_time_analysis_template(
        self, reports: List[WeeklyReport], config_overrides: Dict[str, Any]
    ) -> Dict[str, Any]:
        """创建时间分析模板"""
        # 提取时间数据
        time_data = []
        for report in reports:
            for item in report.work_items:
                time_data.append(
                    {
                        "duration": item.duration_hours,
                        "complexity": item.complexity.value,
                        "category": item.category.value,
                        "employee": report.employee.name,
                    }
                )

        data = pd.DataFrame(time_data)

        # 默认配置
        default_config = VisualizationConfig(
            chart_type="box",
            title="任务时长分析",
            width=800,
            height=400,
            color_scheme="default",
        )

        # 应用覆盖配置
        config_dict = default_config.__dict__.copy()
        config_dict.update(config_overrides)
        config = VisualizationConfig(**config_dict)

        return {"data": data, "config": config}

    def _create_employee_performance_template(
        self, reports: List[WeeklyReport], config_overrides: Dict[str, Any]
    ) -> Dict[str, Any]:
        """创建员工绩效模板"""
        # 计算员工绩效指标
        performance_data = []
        for report in reports:
            total_hours = sum(item.duration_hours for item in report.work_items)
            avg_complexity = (
                sum(item.calculate_complexity_score() for item in report.work_items)
                / len(report.work_items)
                if report.work_items
                else 0
            )

            performance_data.append(
                {
                    "x": report.employee.name,
                    "y": total_hours,
                    "complexity_score": avg_complexity,
                    "task_count": len(report.work_items),
                    "department": report.employee.department,
                }
            )

        data = pd.DataFrame(performance_data)

        # 默认配置
        default_config = VisualizationConfig(
            chart_type="bubble",
            title="员工绩效分析",
            width=800,
            height=500,
            color_scheme="professional",
        )

        # 应用覆盖配置
        config_dict = default_config.__dict__.copy()
        config_dict.update(config_overrides)
        config = VisualizationConfig(**config_dict)

        return {"data": data, "config": config}

    def _create_weekly_summary_template(
        self, reports: List[WeeklyReport], config_overrides: Dict[str, Any]
    ) -> Dict[str, Any]:
        """创建周度总结模板"""
        # 按周汇总数据
        weekly_summary = {}
        for report in reports:
            week = report.week
            if week not in weekly_summary:
                weekly_summary[week] = {
                    "total_hours": 0,
                    "total_tasks": 0,
                    "employees": set(),
                    "departments": set(),
                }

            weekly_summary[week]["total_hours"] += sum(
                item.duration_hours for item in report.work_items
            )
            weekly_summary[week]["total_tasks"] += len(report.work_items)
            weekly_summary[week]["employees"].add(report.employee.email)
            weekly_summary[week]["departments"].add(report.employee.department)

        # 转换为DataFrame
        summary_data = []
        for week, stats in weekly_summary.items():
            summary_data.append(
                {
                    "x": week,
                    "y": stats["total_hours"],
                    "task_count": stats["total_tasks"],
                    "employee_count": len(stats["employees"]),
                    "department_count": len(stats["departments"]),
                }
            )

        data = pd.DataFrame(summary_data)

        # 默认配置
        default_config = VisualizationConfig(
            chart_type="line",
            title="周度工作总结",
            width=800,
            height=400,
            color_scheme="professional",
        )

        # 应用覆盖配置
        config_dict = default_config.__dict__.copy()
        config_dict.update(config_overrides)
        config = VisualizationConfig(**config_dict)

        return {"data": data, "config": config}

    def get_available_templates(self) -> List[str]:
        """获取可用的图表模板"""
        return list(self.chart_templates.keys())

    def get_color_schemes(self) -> Dict[str, List[str]]:
        """获取可用的颜色方案"""
        return self.color_schemes.copy()
