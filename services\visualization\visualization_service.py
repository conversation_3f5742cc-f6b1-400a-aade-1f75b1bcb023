#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
可视化服务主类

模块描述: 统一可视化入口，协调各种可视化器，提供高级可视化功能
作者: 开发团队
创建时间: 2025-01-27
版本: 1.0.0
依赖: core.base, core.interfaces, domain.entities, domain.value_objects
"""

from typing import List, Dict, Any, Optional, Union
from datetime import datetime
import asyncio

from core.base import BaseService
from core.interfaces import IVisualizer
from core.decorators import performance_monitor, cache_result, error_handler
from core.exceptions import VisualizationError, ValidationError
from domain.entities import WeeklyReport, AnalysisResult
from domain.value_objects import VisualizationConfig, ProcessingContext


class VisualizationService(BaseService):
    """
    可视化服务 - 统一可视化入口，协调各种可视化器
    
    职责：
    - 管理和协调各种可视化器
    - 提供统一的可视化接口
    - 支持多种图表类型
    - 缓存可视化结果
    - 生成仪表盘
    """
    
    def __init__(self, config, visualizers: List[IVisualizer] = None, cache_manager=None):
        """
        初始化可视化服务
        
        Args:
            config: 服务配置
            visualizers: 可视化器列表
            cache_manager: 缓存管理器
        """
        super().__init__(config)
        self.visualizers = {viz.get_name(): viz for viz in (visualizers or [])}
        self.cache_manager = cache_manager
        self._visualization_history: List[Dict[str, Any]] = []
        
        # 支持的图表类型
        self.supported_chart_types = set()
        self._update_supported_chart_types()
    
    def initialize(self) -> bool:
        """初始化服务"""
        try:
            # 初始化所有可视化器
            for name, visualizer in self.visualizers.items():
                if hasattr(visualizer, 'initialize'):
                    if not visualizer.initialize():
                        self.logger.error(f"可视化器初始化失败: {name}")
                        return False
                self.logger.info(f"可视化器初始化成功: {name}")
            
            self.logger.info("可视化服务初始化完成")
            return True
        except Exception as e:
            self.logger.error(f"可视化服务初始化失败: {e}")
            return False
    
    def validate_config(self) -> bool:
        """验证配置"""
        if not self.config.name:
            return False
        return True
    
    def get_service_info(self) -> Dict[str, Any]:
        """获取服务信息"""
        return {
            'service_name': self.config.name,
            'version': self.config.version,
            'visualizers': list(self.visualizers.keys()),
            'visualizer_count': len(self.visualizers),
            'supported_chart_types': list(self.supported_chart_types),
            'visualization_history_count': len(self._visualization_history),
            'cache_enabled': self.cache_manager is not None
        }
    
    def add_visualizer(self, visualizer: IVisualizer) -> None:
        """添加可视化器"""
        name = visualizer.get_name()
        self.visualizers[name] = visualizer
        self._update_supported_chart_types()
        self.logger.info(f"添加可视化器: {name}")
    
    def remove_visualizer(self, visualizer_name: str) -> bool:
        """移除可视化器"""
        if visualizer_name in self.visualizers:
            del self.visualizers[visualizer_name]
            self._update_supported_chart_types()
            self.logger.info(f"移除可视化器: {visualizer_name}")
            return True
        return False
    
    @performance_monitor(log_execution_time=True)
    @cache_result(ttl=1800)  # 缓存30分钟
    @error_handler(reraise=True)
    def create_chart(self, data: Any, config: VisualizationConfig, 
                    context: ProcessingContext = None) -> Any:
        """
        创建图表
        
        Args:
            data: 要可视化的数据
            config: 可视化配置
            context: 处理上下文
            
        Returns:
            Any: 图表对象
        """
        # 验证配置
        if not self._validate_visualization_config(config):
            raise ValidationError("可视化配置无效")
        
        # 选择合适的可视化器
        visualizer = self._select_visualizer(config.chart_type)
        if not visualizer:
            raise VisualizationError(f"不支持的图表类型: {config.chart_type}")
        
        # 验证数据
        if not visualizer.validate_data(data, config.chart_type):
            raise ValidationError(f"数据不适合 {config.chart_type} 图表类型")
        
        start_time = datetime.now()
        
        try:
            # 创建可视化
            chart = visualizer.create_visualization(data, config)
            
            # 记录可视化历史
            processing_time = (datetime.now() - start_time).total_seconds()
            self._record_visualization_history(config, processing_time, True)
            
            return chart
            
        except Exception as e:
            processing_time = (datetime.now() - start_time).total_seconds()
            self._record_visualization_history(config, processing_time, False, str(e))
            raise VisualizationError(f"图表创建失败: {e}")
    
    @performance_monitor(log_execution_time=True)
    def create_dashboard(self, reports: List[WeeklyReport], 
                        analysis_results: List[AnalysisResult] = None,
                        dashboard_config: Dict[str, Any] = None) -> Dict[str, Any]:
        """
        创建仪表盘
        
        Args:
            reports: 周报列表
            analysis_results: 分析结果列表
            dashboard_config: 仪表盘配置
            
        Returns:
            Dict[str, Any]: 仪表盘数据
        """
        if not reports:
            raise ValidationError("周报数据不能为空")
        
        dashboard_config = dashboard_config or {}
        dashboard = {
            'title': dashboard_config.get('title', '工作分析仪表盘'),
            'created_at': datetime.now().isoformat(),
            'charts': {},
            'summary': {},
            'metadata': dashboard_config
        }
        
        try:
            # 1. 工作量趋势图
            workload_chart = self._create_workload_trend_chart(reports)
            dashboard['charts']['workload_trend'] = workload_chart
            
            # 2. 任务分布饼图
            task_distribution_chart = self._create_task_distribution_chart(reports)
            dashboard['charts']['task_distribution'] = task_distribution_chart
            
            # 3. 复杂度分析柱状图
            complexity_chart = self._create_complexity_analysis_chart(reports)
            dashboard['charts']['complexity_analysis'] = complexity_chart
            
            # 4. 部门对比图
            department_comparison_chart = self._create_department_comparison_chart(reports)
            dashboard['charts']['department_comparison'] = department_comparison_chart
            
            # 5. 如果有分析结果，添加分析图表
            if analysis_results:
                analysis_charts = self._create_analysis_charts(analysis_results)
                dashboard['charts'].update(analysis_charts)
            
            # 6. 生成摘要信息
            dashboard['summary'] = self._generate_dashboard_summary(reports, analysis_results)
            
            return dashboard
            
        except Exception as e:
            raise VisualizationError(f"仪表盘创建失败: {e}")
    
    def export_chart(self, chart: Any, format: str, filename: str) -> bool:
        """
        导出图表
        
        Args:
            chart: 图表对象
            format: 导出格式
            filename: 文件名
            
        Returns:
            bool: 导出是否成功
        """
        # 尝试使用各个可视化器的导出功能
        for visualizer in self.visualizers.values():
            if hasattr(visualizer, 'export_chart'):
                try:
                    if visualizer.export_chart(chart, format, filename):
                        self.logger.info(f"图表导出成功: {filename}")
                        return True
                except Exception as e:
                    self.logger.warning(f"可视化器 {visualizer.get_name()} 导出失败: {e}")
        
        self.logger.error(f"所有可视化器都无法导出图表: {filename}")
        return False
    
    def get_supported_chart_types(self) -> List[str]:
        """获取支持的图表类型"""
        return list(self.supported_chart_types)
    
    def get_visualizer_info(self, visualizer_name: str) -> Optional[Dict[str, Any]]:
        """获取可视化器信息"""
        if visualizer_name not in self.visualizers:
            return None
        
        visualizer = self.visualizers[visualizer_name]
        return {
            'name': visualizer.get_name(),
            'supported_chart_types': visualizer.get_supported_chart_types(),
            'description': getattr(visualizer, 'get_description', lambda: '')(),
            'version': getattr(visualizer, 'get_version', lambda: '1.0.0')()
        }
    
    def get_visualization_statistics(self) -> Dict[str, Any]:
        """获取可视化统计信息"""
        if not self._visualization_history:
            return {'total_visualizations': 0}
        
        total_visualizations = len(self._visualization_history)
        successful_visualizations = sum(1 for h in self._visualization_history if h['success'])
        avg_processing_time = sum(h['processing_time'] for h in self._visualization_history) / total_visualizations
        
        chart_type_usage = {}
        for history in self._visualization_history:
            chart_type = history['chart_type']
            chart_type_usage[chart_type] = chart_type_usage.get(chart_type, 0) + 1
        
        return {
            'total_visualizations': total_visualizations,
            'successful_visualizations': successful_visualizations,
            'success_rate': successful_visualizations / total_visualizations * 100,
            'average_processing_time': avg_processing_time,
            'chart_type_usage': chart_type_usage,
            'last_visualization': self._visualization_history[-1]['timestamp'].isoformat()
        }
    
    def _update_supported_chart_types(self) -> None:
        """更新支持的图表类型"""
        self.supported_chart_types.clear()
        for visualizer in self.visualizers.values():
            chart_types = visualizer.get_supported_chart_types()
            self.supported_chart_types.update(chart_types)
    
    def _validate_visualization_config(self, config: VisualizationConfig) -> bool:
        """验证可视化配置"""
        if not config.chart_type:
            return False
        if not config.title:
            return False
        if config.width <= 0 or config.height <= 0:
            return False
        return True
    
    def _select_visualizer(self, chart_type: str) -> Optional[IVisualizer]:
        """选择合适的可视化器"""
        for visualizer in self.visualizers.values():
            if chart_type in visualizer.get_supported_chart_types():
                return visualizer
        return None
    
    def _record_visualization_history(self, config: VisualizationConfig, 
                                    processing_time: float, success: bool, 
                                    error_message: str = None) -> None:
        """记录可视化历史"""
        history_entry = {
            'timestamp': datetime.now(),
            'chart_type': config.chart_type,
            'title': config.title,
            'processing_time': processing_time,
            'success': success,
            'error_message': error_message
        }
        
        self._visualization_history.append(history_entry)
        
        # 保持历史记录在合理范围内
        if len(self._visualization_history) > 1000:
            self._visualization_history = self._visualization_history[-500:]
    
    def _create_workload_trend_chart(self, reports: List[WeeklyReport]) -> Dict[str, Any]:
        """创建工作量趋势图"""
        # 提取工作量数据
        workload_data = []
        for report in reports:
            total_hours = sum(item.duration_hours for item in report.work_items)
            workload_data.append({
                'week': report.week,
                'employee': report.employee.name,
                'total_hours': total_hours,
                'task_count': len(report.work_items)
            })
        
        config = VisualizationConfig(
            chart_type='line',
            title='工作量趋势分析',
            width=800,
            height=400
        )
        
        return self.create_chart(workload_data, config)
    
    def _create_task_distribution_chart(self, reports: List[WeeklyReport]) -> Dict[str, Any]:
        """创建任务分布饼图"""
        # 统计任务类别分布
        category_counts = {}
        for report in reports:
            for item in report.work_items:
                category = item.category.value
                category_counts[category] = category_counts.get(category, 0) + 1
        
        distribution_data = [
            {'category': category, 'count': count}
            for category, count in category_counts.items()
        ]
        
        config = VisualizationConfig(
            chart_type='pie',
            title='任务类别分布',
            width=600,
            height=400
        )
        
        return self.create_chart(distribution_data, config)
    
    def _create_complexity_analysis_chart(self, reports: List[WeeklyReport]) -> Dict[str, Any]:
        """创建复杂度分析柱状图"""
        # 统计复杂度分布
        complexity_data = []
        for report in reports:
            complexity_dist = report.get_complexity_distribution()
            complexity_data.append({
                'employee': report.employee.name,
                'week': report.week,
                **complexity_dist
            })
        
        config = VisualizationConfig(
            chart_type='bar',
            title='任务复杂度分析',
            width=800,
            height=500
        )
        
        return self.create_chart(complexity_data, config)
    
    def _create_department_comparison_chart(self, reports: List[WeeklyReport]) -> Dict[str, Any]:
        """创建部门对比图"""
        # 按部门统计数据
        department_data = {}
        for report in reports:
            dept = report.employee.department
            if dept not in department_data:
                department_data[dept] = {'total_hours': 0, 'task_count': 0, 'employee_count': set()}
            
            department_data[dept]['total_hours'] += sum(item.duration_hours for item in report.work_items)
            department_data[dept]['task_count'] += len(report.work_items)
            department_data[dept]['employee_count'].add(report.employee.email)
        
        # 转换为图表数据
        comparison_data = []
        for dept, data in department_data.items():
            comparison_data.append({
                'department': dept,
                'avg_hours_per_employee': data['total_hours'] / len(data['employee_count']),
                'avg_tasks_per_employee': data['task_count'] / len(data['employee_count']),
                'employee_count': len(data['employee_count'])
            })
        
        config = VisualizationConfig(
            chart_type='bar',
            title='部门工作量对比',
            width=800,
            height=500
        )
        
        return self.create_chart(comparison_data, config)
    
    def _create_analysis_charts(self, analysis_results: List[AnalysisResult]) -> Dict[str, Any]:
        """创建分析结果图表"""
        charts = {}
        
        # 按分析类型分组
        results_by_type = {}
        for result in analysis_results:
            analysis_type = result.analysis_type
            if analysis_type not in results_by_type:
                results_by_type[analysis_type] = []
            results_by_type[analysis_type].append(result)
        
        # 为每种分析类型创建图表
        for analysis_type, results in results_by_type.items():
            try:
                chart_data = self._extract_analysis_chart_data(results)
                config = VisualizationConfig(
                    chart_type='scatter',
                    title=f'{analysis_type} 分析结果',
                    width=600,
                    height=400
                )
                charts[f'{analysis_type}_analysis'] = self.create_chart(chart_data, config)
            except Exception as e:
                self.logger.warning(f"创建 {analysis_type} 分析图表失败: {e}")
        
        return charts
    
    def _extract_analysis_chart_data(self, results: List[AnalysisResult]) -> List[Dict[str, Any]]:
        """提取分析结果的图表数据"""
        chart_data = []
        for result in results:
            chart_data.append({
                'result_id': result.result_id,
                'confidence_score': result.confidence_score,
                'processing_time': result.processing_time,
                'created_at': result.created_at.isoformat()
            })
        return chart_data
    
    def _generate_dashboard_summary(self, reports: List[WeeklyReport], 
                                  analysis_results: List[AnalysisResult] = None) -> Dict[str, Any]:
        """生成仪表盘摘要"""
        total_reports = len(reports)
        total_employees = len(set(report.employee.email for report in reports))
        total_hours = sum(sum(item.duration_hours for item in report.work_items) for report in reports)
        total_tasks = sum(len(report.work_items) for report in reports)
        
        summary = {
            'total_reports': total_reports,
            'total_employees': total_employees,
            'total_hours': total_hours,
            'total_tasks': total_tasks,
            'avg_hours_per_employee': total_hours / total_employees if total_employees > 0 else 0,
            'avg_tasks_per_employee': total_tasks / total_employees if total_employees > 0 else 0
        }
        
        if analysis_results:
            avg_confidence = sum(result.confidence_score for result in analysis_results) / len(analysis_results)
            summary['analysis_results_count'] = len(analysis_results)
            summary['avg_analysis_confidence'] = avg_confidence
        
        return summary
    
    def _cleanup(self) -> None:
        """清理资源"""
        # 清理可视化器
        for visualizer in self.visualizers.values():
            if hasattr(visualizer, 'cleanup'):
                try:
                    visualizer.cleanup()
                except Exception as e:
                    self.logger.error(f"可视化器清理失败: {e}")
        
        # 清理历史记录
        self._visualization_history.clear()
        
        self.logger.info("可视化服务资源清理完成")
